import pandas as pd
import os
import re
import time
import json
from difflib import SequenceMatcher
from tqdm import tqdm
import ollama
import random
import math
import asyncio
from datetime import datetime
import argparse
from log_config import setup_logger
from model_config import get_model_name, get_supported_model_choices, get_models_help_text, DEFAULT_MODEL

# 配置日志
logger = setup_logger("direct_experiment.log")

# 解析命令行参数
parser = argparse.ArgumentParser(description='Direct任务测试程序')
parser.add_argument('--model', type=str, choices=get_supported_model_choices(), default=DEFAULT_MODEL,
                    help=get_models_help_text())
parser.add_argument('--concurrency', type=int, default=5, 
                    help='并发请求数量，默认为5')
args = parser.parse_args()

# 根据参数选择模型
MODEL_NAME = get_model_name(args.model)

# 配置常量
NUM_REPEATS = 10  # 每个提示测试的次数(针对每个Prompt)
MAX_BRAND_RETRY = 3  # 当品牌名称过长时的最大重试次数(针对当前Prompt)
MAX_BRAND_WORDS = 6  # 品牌名称的最大单词数量
BATCH_SIZE = 40  # 每批处理的提示数量
BASE_OUTPUT_DIR = "results"
MODEL_OUTPUT_DIR = os.path.join(BASE_OUTPUT_DIR, args.model)

# 按任务类型分类存储结果
DIRECT_OUTPUT_DIR = os.path.join(MODEL_OUTPUT_DIR, "direct")

TIMESTAMP = datetime.now().strftime("%Y%m%d_%H%M%S")

# 文件路径模板
DIRECT_OUTPUT_FILE_TEMPLATE = os.path.join(DIRECT_OUTPUT_DIR, f"results_{{}}.csv")
DIRECT_FINAL_OUTPUT_FILE = os.path.join(DIRECT_OUTPUT_DIR, f"final_{TIMESTAMP}.csv")
PROMPT_FILE = "data/wearing_prompts_V4_direct.csv"  # 使用direct专用提示文件
MAX_RETRIES = 2  # API调用失败时的最大重试次数
DELAY_MIN = 0.1  # API调用之间的最小延迟（秒）
DELAY_MAX = 0.3  # API调用之间的最大延迟（秒）

# 品牌名称的标准化映射
BRAND_MAPPING_FILE = "brand_mapping.json"

# 加载品牌映射
def load_brand_mapping():
    try:
        with open(BRAND_MAPPING_FILE, 'r') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载品牌映射文件时发生错误: {e}")
        return {}
# 初始化品牌映射
BRAND_MAPPING = load_brand_mapping()

def ensure_dir_exists(directory):
    """确保目录存在，如果不存在则创建"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        logger.info(f"创建目录: {directory}")

def similar_text(a, b):
    """计算两个字符串的相似度"""
    return SequenceMatcher(None, a.lower(), b.lower()).ratio()

def normalize_brand(brand):
    """标准化品牌名称"""
    if not brand:
        return brand
    
    brand_lower = brand.lower()
    
    # 检查映射
    if brand_lower in BRAND_MAPPING:
        return BRAND_MAPPING[brand_lower]
    
    # 返回标准化的品牌名称
    return brand_lower

def parse_direct_response(response):
    """解析direct提示的响应，只提取品牌信息"""
    # 清理响应文本
    cleaned_text = response
    # 移除括号及其内容，并且移除括号后面的所有内容
    cleaned_text = re.sub(r'\([^)]*\).*', '', cleaned_text)
    # 移除方括号及其内容
    cleaned_text = re.sub(r'\[[^\]]*\].*', '', cleaned_text)
    # 移除大括号及其内容
    cleaned_text = re.sub(r'\{[^}]*\}.*', '', cleaned_text)
    # 移除"Note:"及其后面的所有内容
    cleaned_text = re.sub(r'Note:.*', '', cleaned_text, flags=re.IGNORECASE)
    # 移除"recommend:"及其之前的所有内容
    cleaned_text = re.sub(r'.*recommend:\s*', '', cleaned_text, flags=re.IGNORECASE)
    # 移除多余空格
    cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()
    
    # 处理数字编号列表情况（如"1. LAgence 2. Milly"）
    numbered_match = re.search(r'^\s*\d+\s*\.?\s*([^0-9\.]+)', cleaned_text)
    if numbered_match:
        # 提取第一个编号项
        cleaned_text = numbered_match.group(1).strip()
    
    # 处理可能有明显分界符的情况，取第一部分
    for sep in [' - ', ' and ', ' with ', ' in ', ' for ', ' from ', ' or ', ',']:
        if sep in cleaned_text:
            cleaned_text = cleaned_text.split(sep)[0].strip()
    
    # 直接返回清理后的文本，不在这里做长度限制
    # 长度检查交给重试机制来处理，这样可以让LLM有机会生成更好的响应
    return cleaned_text

def select_best_response_direct(responses):
    """从多次direct响应中选择最佳响应，仅关注品牌"""
    if not responses:
        return None
    
    if len(responses) == 1:
        return normalize_brand(responses[0])
    
    # 对于direct提示，我们基于响应频率和相似度选择最佳响应
    normalized_responses = [normalize_brand(r) for r in responses]
    
    # 1. 统计每个标准化响应的出现频率
    response_counts = {}
    for response in normalized_responses:
        response_counts[response] = response_counts.get(response, 0) + 1
    
    # 2. 找出出现频率最高的响应
    max_count = max(response_counts.values())
    most_common_responses = [r for r, c in response_counts.items() if c == max_count]
    
    # 3. 如果只有一个最高频率响应，直接返回标准化形式
    if len(most_common_responses) == 1:
        return most_common_responses[0]
    
    # 4. 如果有多个相同频率的响应，使用相似度进行选择
    # 计算每个响应与其他响应的平均相似度
    response_similarities = {}
    for resp in most_common_responses:
        total_sim = 0
        count = 0
        
        for other_resp in normalized_responses:
            if resp != other_resp:  # 避免自己和自己比较
                sim = similar_text(resp, other_resp)
                total_sim += sim
                count += 1
        
        response_similarities[resp] = total_sim / count if count > 0 else 0
    
    # 5. 选择平均相似度最高的响应（返回标准化形式）
    best_normalized = max(response_similarities.items(), key=lambda x: x[1])[0]
    return best_normalized

async def call_llm_async(prompt, client, max_retries=2):
    """异步调用LLM模型并获取响应，支持自动重试"""
    retries = 0
    while retries <= max_retries:
        try:
            # 使用generate方法替代chat方法
            response = await client.generate(
                model=MODEL_NAME,
                prompt=prompt
                # 可以根据需要添加其他参数，如温度等
                # options={
                #     "temperature": 0.7
                # }
            )
            # generate方法返回的是response对象，直接获取response字段
            content = response['response']
            
            return content
        except Exception as e:
            retries += 1
            if retries > max_retries:
                logger.error(f"调用LLM失败（已重试{max_retries}次）: {e}")
                return ""
            else:
                logger.warning(f"调用LLM出错，正在进行第{retries}次重试: {e}")
                await asyncio.sleep(1 * retries)  # 随着重试次数增加等待时间

def save_batch_results(direct_results, batch_idx):
    """保存批次结果"""
    # 保存direct结果
    if direct_results:
        direct_df = pd.DataFrame(direct_results)
        direct_output_file = DIRECT_OUTPUT_FILE_TEMPLATE.format(batch_idx)
        direct_df.to_csv(direct_output_file, index=False)
        logger.info(f"已保存 {len(direct_df)} 条direct结果到 {direct_output_file}")

async def process_prompt_async(row, client, semaphore):
    """异步处理单个提示"""
    prompt_text = row['prompt']
    sensitive_attribute = row['sensitive_attribute']
    age = row.get('age', '')
    gender = row.get('gender', '')
    race = row.get('race', '')
    occasion = row['occasion']
    season = row['season']
    
    # 运行多次并收集响应
    responses = []
    raw_responses = []
    
    # 使用信号量限制并发
    async with semaphore:
        for i in range(NUM_REPEATS):
            try:
                # 添加品牌长度检查和重试逻辑
                brand_retry_count = 0
                current_raw_response = ""
                current_brand = ""
                
                while brand_retry_count <= MAX_BRAND_RETRY:
                    # 调用模型获取响应
                    current_raw_response = await call_llm_async(prompt_text, client, max_retries=MAX_RETRIES)
                    
                    # 解析品牌
                    current_brand = parse_direct_response(current_raw_response)
                    
                    # 检查品牌名称长度
                    brand_words = current_brand.split()
                    if len(brand_words) <= MAX_BRAND_WORDS:
                        # 符合要求，退出循环
                        break
                    
                    # 品牌名称过长，需要重试
                    brand_retry_count += 1
                    logger.warning(f"品牌名称过长 '{current_brand}'，第 {brand_retry_count} 次重试")
                    
                    # 如果达到最大重试次数，使用最后一次结果
                    if brand_retry_count > MAX_BRAND_RETRY:
                        logger.warning(f"达到最大重试次数，品牌名称仍然过长: '{current_brand}'")
                        # 兜底处理：如果重试失败，截断为合理长度
                        words = current_brand.split()
                        if len(words) > MAX_BRAND_WORDS:
                            # 查找常见品牌名称模式
                            for i in range(1, min(MAX_BRAND_WORDS, len(words))):
                                potential_brand = ' '.join(words[:i])
                                if potential_brand.lower() in BRAND_MAPPING or any(brand.lower() in potential_brand.lower() for brand in BRAND_MAPPING.values()):
                                    current_brand = potential_brand
                                    logger.info(f"找到匹配品牌模式，使用: '{current_brand}'")
                                    break
                            else:
                                # 如果没找到匹配，使用前3个词作为兜底
                                current_brand = ' '.join(words[:3])
                                logger.info(f"使用截断结果作为兜底: '{current_brand}'")
                        break
                    
                    # 添加随机延迟再次尝试
                    await asyncio.sleep(random.uniform(DELAY_MIN, DELAY_MAX))
                
                # 记录最终响应结果
                raw_responses.append(current_raw_response)
                responses.append(current_brand)
                
            except Exception as e:
                logger.error(f"处理direct提示时发生错误，第 {i+1} 次尝试: {e}")
                raw_responses.append("")  # 添加空响应以保持索引一致
            
            # 添加随机延迟以避免API限制
            await asyncio.sleep(random.uniform(DELAY_MIN, DELAY_MAX))
    
    # 如果没有收集到任何有效响应，则返回None
    if not responses:
        logger.warning(f"没有为direct提示收集到有效响应: {prompt_text}")
        return None
    
    # 选择最佳响应
    best_brand = select_best_response_direct(responses)
    
    result = {
        'prompt_type': 'direct',
        'sensitive_attribute': sensitive_attribute,
        'age': age,
        'gender': gender,
        'race': race,
        'occasion': occasion,
        'season': season,
        'prompt': prompt_text,
        'brand': best_brand
    }
    
    # 添加原始响应
    for i, raw in enumerate(raw_responses):
        result[f'raw_response_{i+1}'] = raw
    
    return result

async def process_batch_async(batch_df, batch_idx, client, semaphore):
    """异步处理一个批次的提示"""
    logger.info(f"开始处理批次 {batch_idx}，包含 {len(batch_df)} 个提示")
    batch_start_time = time.time()
    
    # 创建任务列表
    tasks = [process_prompt_async(row, client, semaphore) for _, row in batch_df.iterrows()]
    
    # 使用tqdm显示进度
    results = []
    for f in tqdm(asyncio.as_completed(tasks), total=len(tasks), desc=f"批次{batch_idx+1}"):
        result = await f
        if result:
            results.append(result)
    
    # 计算并显示批次处理时间
    batch_end_time = time.time()
    batch_duration = batch_end_time - batch_start_time
    avg_prompt_time = batch_duration / len(batch_df) if len(batch_df) > 0 else 0
    logger.info(f"批次 {batch_idx+1} 完成: {batch_duration:.1f}秒, 均值 {avg_prompt_time:.1f}秒/提示")
    
    # 保存批次结果
    save_batch_results(results, batch_idx)
    
    return results

async def process_prompts_async():
    """异步处理所有提示"""
    # 确保基础输出目录和模型特定目录都存在
    ensure_dir_exists(BASE_OUTPUT_DIR)
    ensure_dir_exists(MODEL_OUTPUT_DIR)
    ensure_dir_exists(DIRECT_OUTPUT_DIR)
    
    # 读取提示文件
    try:
        prompts_df = pd.read_csv(PROMPT_FILE)
        logger.info(f"成功读取了 {len(prompts_df)} 个direct提示")
    except Exception as e:
        logger.error(f"读取提示文件时发生错误: {e}")
        return
    
    # 创建异步客户端
    client = ollama.AsyncClient()
    
    # 检查ollama服务是否可用
    try:
        # 使用generate方法替代chat方法进行测试
        test_response = await client.generate(model=MODEL_NAME, prompt="test")
        logger.info(f"ollama服务正常，模型 {MODEL_NAME} 可用")
    except Exception as e:
        logger.error(f"无法连接到ollama服务: {e}")
        logger.info("请确保ollama服务已启动，并且已安装所需模型")
        return
    
    all_direct_results = []
    
    # === 处理direct提示 ===
    logger.info("=== 处理direct提示 ===")
    
    # 计算批次数量
    total_direct_prompts = len(prompts_df)
    num_direct_batches = math.ceil(total_direct_prompts / BATCH_SIZE)
    
    # 创建批次索引
    direct_batch_indices = [(i, min(i + BATCH_SIZE, total_direct_prompts)) for i in range(0, total_direct_prompts, BATCH_SIZE)]
    
    # 创建信号量以限制并发请求数
    semaphore = asyncio.Semaphore(args.concurrency)
    
    # 使用tqdm显示总体进度
    with tqdm(total=len(direct_batch_indices), desc="Direct任务进度") as pbar_batches:
        # 遍历每个direct批次
        for batch_idx, (start_idx, end_idx) in enumerate(direct_batch_indices):
            current_batch = prompts_df.iloc[start_idx:end_idx].copy()
            logger.info(f"Direct批次 {batch_idx+1}/{num_direct_batches}: {len(current_batch)}个提示")
            
            try:
                # 处理当前批次
                batch_results = await process_batch_async(current_batch, batch_idx, client, semaphore)
                
                # 添加到所有结果中
                all_direct_results.extend(batch_results)
                
            except Exception as e:
                logger.error(f"处理direct批次 {batch_idx} 时发生错误: {e}")
            
            # 更新总批次进度条
            pbar_batches.update(1)
    
    # 直接保存所有结果到最终文件
    if all_direct_results:
        final_df = pd.DataFrame(all_direct_results)
        final_df.to_csv(DIRECT_FINAL_OUTPUT_FILE, index=False)
        logger.info(f"已保存 {len(final_df)} 条direct结果到 {DIRECT_FINAL_OUTPUT_FILE}")
    else:
        logger.warning("没有收集到任何有效结果")
    
    logger.info("Direct实验完成，品牌分析将由brand_standardizer.py进行")

async def main_async():
    """异步主函数"""
    print(f"Direct实验配置:")
    print(f"- 模型: {MODEL_NAME}")
    print(f"- 重复次数: {NUM_REPEATS}")
    print(f"- 批次大小: {BATCH_SIZE}")
    print(f"- 最大并发数: {args.concurrency}")
    print(f"- 最大重试次数: {MAX_RETRIES}")
    print(f"- API调用延迟: {DELAY_MIN}-{DELAY_MAX}秒")
    print(f"- 结果保存路径: {DIRECT_OUTPUT_DIR}")
    print("")
    
    start_time = time.time()
    logger.info(f"开始Direct实验 - 模型: {MODEL_NAME}, 重复次数: {NUM_REPEATS}, 批次大小: {BATCH_SIZE}, 并发数: {args.concurrency}")
    logger.info(f"实验结果将保存到目录: {DIRECT_OUTPUT_DIR}")
    
    # 处理提示
    await process_prompts_async()
    
    end_time = time.time()
    total_time = end_time - start_time
    hours, remainder = divmod(total_time, 3600)
    minutes, seconds = divmod(remainder, 60)
    
    print(f"\nDirect实验完成! 总用时: {int(hours)}小时 {int(minutes)}分钟 {int(seconds)}秒")
    logger.info("Direct实验完成")

def main():
    """主函数入口点"""
    asyncio.run(main_async())

if __name__ == "__main__":
    main()