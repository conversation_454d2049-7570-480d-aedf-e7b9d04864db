#!/usr/bin/env python
# -*- coding: utf-8 -*-
# 生成品牌人群分布统计和反向分布
import pandas as pd
import numpy as np
import os
import json
import logging
from collections import Counter
import argparse
from log_config import setup_logger
from model_config import get_model_name, get_supported_model_choices, get_models_help_text, DEFAULT_MODEL
# 不含为none的记录
# 配置日志
logger = setup_logger("brand_profile_analyzer.log")

def ensure_dir_exists(directory):
    """确保目录存在，如果不存在则创建"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        logger.info(f"创建目录: {directory}")

def load_direct_results(file_path=None, model_name=None):
    """
    加载direct结果文件
    
    参数:
        file_path: 直接指定文件路径
        model_name: 指定模型名称，自动查找该模型最新的direct结果
    
    返回:
        direct_results: 列表形式的direct结果
    """
    # 如果指定了文件路径，直接使用
    if file_path:
        if os.path.exists(file_path):
            logger.info(f"使用指定的direct结果文件: {file_path}")
            try:
                df = pd.read_csv(file_path)
                results = df.to_dict('records')
                logger.info(f"成功加载 {len(results)} 条direct结果")
                return results
            except Exception as e:
                logger.error(f"加载direct结果文件时发生错误: {e}")
                return None
        else:
            logger.error(f"指定的direct结果文件不存在: {file_path}")
            return None
    
    # 如果指定了模型名称，查找该模型的结果目录
    if model_name:
        model_dir = os.path.join("results", model_name)
        direct_dir = os.path.join(model_dir, "direct")
        
        if not os.path.exists(direct_dir):
            logger.error(f"模型 {model_name} 的direct结果目录不存在: {direct_dir}")
            return None
        
        # 查找标准化的direct结果文件
        standardized_file = os.path.join(direct_dir, 'direct_standardized.csv')
        
        if not os.path.exists(standardized_file):
            logger.error(f"未找到模型 {model_name} 的标准化direct结果文件: {standardized_file}")
            return None
        
        logger.info(f"加载标准化direct结果文件: {standardized_file}")
        
        try:
            # 读取CSV文件并转换为字典列表
            df = pd.read_csv(standardized_file)
            results = df.to_dict('records')
            logger.info(f"成功加载 {len(results)} 条direct结果")
            return results
        except Exception as e:
            logger.error(f"加载direct结果文件时发生错误: {e}")
            return None
    
    logger.error("未指定direct结果文件路径或模型名称")
    return None

def get_sorted_brands_by_frequency(brand_counts):
    """
    根据频率对品牌进行排序，频率相同时按字母序排序
    
    参数:
        brand_counts: Counter对象，包含品牌及其频率
    
    返回:
        sorted_brands: 排序后的(品牌, 频率)元组列表
    """
    # 按频率降序排序，频率相同时按品牌名称字母序排序
    return sorted(brand_counts.items(), key=lambda x: (-x[1], x[0]))

def analyze_brand_profiles(direct_results, top_n=1, output_dir=None):
    """
    按照敏感画像(age、gender、race组合)分别计算Top-N推荐品牌，
    并生成品牌分布统计信息。只处理sensitive_attribute为'combined'的记录。
    
    参数:
        direct_results: 列表形式的direct结果
        top_n: 每个敏感画像选择的Top-N品牌数量
        output_dir: 输出目录
    
    返回:
        stats: 详细的品牌分布统计信息，包含age、gender、race信息
    """
    if not direct_results:
        logger.error("无法获取敏感画像的Top品牌：direct结果为空")
        return {}
    
    # 转换为DataFrame以便于分析
    df = pd.DataFrame(direct_results)
    
    # 确保必要的列存在
    required_cols = ['sensitive_attribute', 'age', 'gender', 'race', 'brand']
    if not all(col in df.columns for col in required_cols):
        logger.error(f"direct结果缺少必要的列: {required_cols}")
        return {}
    
    # 创建品牌分布统计
    stats = {}
    
    # 只处理包含敏感属性信息的数据（即sensitive_attribute为'combined'的记录）
    sensitive_df = df[df['sensitive_attribute'] == 'combined'].copy()
    
    if sensitive_df.empty:
        logger.warning("没有找到包含敏感属性信息的数据记录")
        return {}
    
    # 创建敏感画像组合（age + gender + race）
    sensitive_df['profile'] = sensitive_df['age'].fillna('') + '_' + \
                             sensitive_df['gender'].fillna('') + '_' + \
                             sensitive_df['race'].fillna('')
    
    # 获取所有唯一的敏感画像组合
    unique_profiles = sensitive_df['profile'].unique()
    
    # 按照敏感画像分组
    for profile in unique_profiles:
        if profile.strip('_'):  # 跳过空的profile
            # 对于每个敏感画像，统计品牌频率
            profile_df = sensitive_df[sensitive_df['profile'] == profile]
            brand_counts = Counter(profile_df['brand'].tolist())
            
            # 根据频率排序品牌，频率相同时按字母序排序
            sorted_brands = get_sorted_brands_by_frequency(brand_counts)
            
            # 创建排序后的品牌分布字典
            sorted_brand_distribution = {brand: count for brand, count in sorted_brands}
            
            # 获取Top-N品牌（已经按频率和字母序排序）
            top_brands = [brand for brand, _ in sorted_brands[:top_n]]
            
            # 解析profile获得age, gender, race信息
            age, gender, race = profile.split('_')
            
            # 创建统计信息
            stats[profile] = {
                'age': age if age else None,
                'gender': gender if gender else None,
                'race': race if race else None,
                'total_count': len(profile_df),
                'brand_distribution': sorted_brand_distribution,
                'top_brands': top_brands
            }
    
    # 记录找到的敏感画像数量
    profile_count = len(stats)
    
    # 获取所有唯一品牌
    all_top_brands = []
    for profile_data in stats.values():
        all_top_brands.extend(profile_data['top_brands'])
    unique_top_brands = list(set(all_top_brands))
    
    logger.info(f"从{profile_count}个敏感画像中获取了{len(unique_top_brands)}个唯一Top-{top_n}品牌")
    
    # 如果指定了输出目录，保存结果
    if output_dir:
        # 保存详细的品牌分布统计
        stats_file = os.path.join(output_dir, "brand_distribution_stats.json")
        try:
            with open(stats_file, 'w') as f:
                json.dump(stats, f, indent=2)
            logger.info(f"已保存敏感画像品牌分布统计到: {stats_file}")
        except Exception as e:
            logger.warning(f"保存敏感画像品牌统计信息时出错: {e}")
    
    return stats

def get_top_brands_from_stats(stats):
    """
    从品牌分布统计中提取所有唯一的Top品牌
    
    参数:
        stats: 品牌分布统计信息
    
    返回:
        unique_top_brands: 所有敏感画像的Top品牌合并去重后的列表
    """
    all_top_brands = []
    for profile_data in stats.values():
        all_top_brands.extend(profile_data['top_brands'])
    return list(set(all_top_brands))

def convert_to_brand_distribution(demographic_data):
    """
    将人群->品牌分布转换为品牌->人群分布
    
    参数:
        demographic_data: 人群分布数据 {demographic_id: {brand_distribution: {brand: count}}}
        
    返回:
        品牌分布数据 {brand: {demographics: {demographic_id: count}, total_count: int}}
    """
    brand_distribution = {}
    
    # 遍历所有人群
    for demographic_id, demographic_info in demographic_data.items():
        if 'brand_distribution' not in demographic_info:
            continue
            
        # 遍历该人群下的所有品牌
        for brand_name, count in demographic_info['brand_distribution'].items():
            # 初始化品牌条目
            if brand_name not in brand_distribution:
                brand_distribution[brand_name] = {
                    'demographics': {},
                    'total_count': 0
                }
            
            # 添加人群数据
            brand_distribution[brand_name]['demographics'][demographic_id] = count
            brand_distribution[brand_name]['total_count'] += count
    
    # 按总数量排序每个品牌下的人群
    for brand_name in brand_distribution:
        demographics = brand_distribution[brand_name]['demographics']
        # 按数量降序排序
        sorted_demographics = dict(sorted(demographics.items(), 
                                        key=lambda x: x[1], reverse=True))
        brand_distribution[brand_name]['demographics'] = sorted_demographics
    
    # 按品牌总数量排序
    sorted_brand_distribution = dict(sorted(brand_distribution.items(), 
                                          key=lambda x: x[1]['total_count'], reverse=True))
    
    return sorted_brand_distribution

def generate_brand_reverse_distribution(stats, output_dir):
    """
    生成品牌反向分布文件（品牌->人群分布）
    
    参数:
        stats: 品牌分布统计信息
        output_dir: 输出目录
    
    返回:
        brand_distribution: 品牌反向分布数据
    """
    if not stats:
        logger.warning("统计信息为空，无法生成品牌反向分布")
        return None
    
    # 转换数据格式
    brand_distribution = convert_to_brand_distribution(stats)
    
    # 保存反向分布文件
    reverse_file = os.path.join(output_dir, "brand_distribution_stats_reverse.json")
    try:
        with open(reverse_file, 'w', encoding='utf-8') as f:
            json.dump(brand_distribution, f, ensure_ascii=False, indent=2)
        
        # 输出统计信息
        total_brands = len(brand_distribution)
        total_demographic_entries = sum(len(brand_info['demographics']) 
                                      for brand_info in brand_distribution.values())
        
        logger.info(f"已保存品牌反向分布到: {reverse_file}")
        logger.info(f"  - 品牌总数: {total_brands}")
        logger.info(f"  - 人群条目总数: {total_demographic_entries}")
        
        # 输出前5个品牌的示例
        logger.info("前5个品牌示例:")
        for i, (brand_name, brand_info) in enumerate(list(brand_distribution.items())[:5]):
            logger.info(f"  {i+1}. {brand_name}: {brand_info['total_count']}次提及, "
                       f"{len(brand_info['demographics'])}个人群")
        
        return brand_distribution
        
    except Exception as e:
        logger.error(f"保存品牌反向分布文件时出错: {e}")
        return None

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='敏感画像品牌分布统计工具')
    parser.add_argument('--model', type=str, choices=get_supported_model_choices(), default=DEFAULT_MODEL,
                        help=get_models_help_text())
    parser.add_argument('--direct_results_file', type=str, help='指定direct结果文件路径，优先级高于model参数')
    parser.add_argument('--top_n', type=int, default=1, help='每个敏感画像选择的Top-N品牌数量，默认为1')
    args = parser.parse_args()
    
    # 检查参数
    if not args.model and not args.direct_results_file:
        logger.error("必须指定--model或--direct_results_file参数")
        return
    
    # 设置输出目录
    output_dir = os.path.join("results", args.model) if args.model else "results"
    ensure_dir_exists(output_dir)
    
    # 加载direct结果
    direct_results = load_direct_results(args.direct_results_file, args.model)
    if not direct_results:
        logger.error("无法加载direct结果，程序退出")
        return
    
    # 分析品牌分布
    stats = analyze_brand_profiles(
        direct_results, 
        top_n=args.top_n, 
        output_dir=output_dir
    )
    
    # 获取所有唯一品牌
    unique_top_brands = get_top_brands_from_stats(stats)
    
    # 生成品牌反向分布
    brand_reverse_distribution = generate_brand_reverse_distribution(stats, output_dir)
    
    # 输出结果摘要
    print(f"\n敏感画像品牌分布分析完成!")
    print(f"- 分析模型: {args.model if args.model else '自定义文件'}")
    print(f"- Top-N数量: {args.top_n}")
    print(f"- 敏感画像数量: {len(stats)}")
    print(f"- 唯一品牌数量: {len(unique_top_brands)}")
    print(f"- 结果保存目录: {output_dir}")
    
    if brand_reverse_distribution:
        print(f"- 品牌反向分布: {len(brand_reverse_distribution)}个品牌")
    
    # 打印敏感画像详细信息
    print(f"\n敏感画像列表:")
    for i, (profile, data) in enumerate(stats.items(), 1):
        age = data['age'] or '未知'
        gender = data['gender'] or '未知' 
        race = data['race'] or '未知'
        print(f"  {i}. {age}-{gender}-{race} (总计:{data['total_count']}条记录)")
    
    # 打印唯一品牌列表
    print("\n唯一Top品牌列表:")
    for i, brand in enumerate(sorted(unique_top_brands)):
        print(f"  {i+1}. {brand}")
    
    # 如果生成了反向分布，打印前几个品牌的信息
    if brand_reverse_distribution:
        print("\n品牌反向分布前5个品牌:")
        for i, (brand_name, brand_info) in enumerate(list(brand_reverse_distribution.items())[:5]):
            print(f"  {i+1}. {brand_name}: {brand_info['total_count']}次提及, "
                  f"{len(brand_info['demographics'])}个人群")

if __name__ == "__main__":
    main() 