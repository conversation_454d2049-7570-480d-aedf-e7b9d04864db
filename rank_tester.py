import pandas as pd
import os
import re
import time
import json
from difflib import SequenceMatcher
from tqdm import tqdm
import ollama
import random
import math
import asyncio
from datetime import datetime
import argparse
from log_config import setup_logger
from model_config import get_model_name, get_supported_model_choices, get_models_help_text, DEFAULT_MODEL

# 配置日志
logger = setup_logger("rank_experiment.log")

# 解析命令行参数
parser = argparse.ArgumentParser(description='Rank任务测试程序')
parser.add_argument('--model', type=str, choices=get_supported_model_choices(), default=DEFAULT_MODEL,
                    help=get_models_help_text())
parser.add_argument('--selected_brands_file', type=str, help='指定选定品牌文件路径，默认自动查找最新文件')
parser.add_argument('--concurrency', type=int, default=5, 
                    help='并发请求数量，默认为5')
args = parser.parse_args()

# 根据参数选择模型
MODEL_NAME = get_model_name(args.model)

# 配置常量
NUM_REPEATS = 5  # 每个提示测试的次数(针对每个Prompt)
BATCH_SIZE = 40  # 每批处理的提示数量
BASE_OUTPUT_DIR = "results"
MODEL_OUTPUT_DIR = os.path.join(BASE_OUTPUT_DIR, args.model)
MAX_CONCURRENCY = args.concurrency  # 最大并发请求数

# 按任务类型分类存储结果
DIRECT_OUTPUT_DIR = os.path.join(MODEL_OUTPUT_DIR, "direct")
RANK_OUTPUT_DIR = os.path.join(MODEL_OUTPUT_DIR, "rank_brands")

TIMESTAMP = datetime.now().strftime("%Y%m%d_%H%M%S")

# 文件路径模板
RANK_OUTPUT_FILE_TEMPLATE = os.path.join(RANK_OUTPUT_DIR, f"results_{{}}.csv")
RANK_FINAL_OUTPUT_FILE = os.path.join(RANK_OUTPUT_DIR, f"final_{TIMESTAMP}.csv")
PROMPT_FILE = "data/wearing_prompts_V4_rank_brands.csv"  # 使用rank专用提示文件
MAX_RETRIES = 2  # API调用失败时的最大重试次数
DELAY_MIN = 0.1  # API调用之间的最小延迟（秒）
DELAY_MAX = 0.3  # API调用之间的最大延迟（秒）
MIN_BRANDS_FOR_RANKING = 5  # 排名任务所需的最少品牌数量
MAX_BRANDS_FOR_RANKING = 10  # 排名任务的最大品牌数量

# 品牌名称的标准化映射
BRAND_MAPPING_FILE = "brand_mapping.json"

# 加载品牌映射
def load_brand_mapping():
    try:
        with open(BRAND_MAPPING_FILE, 'r') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载品牌映射文件时发生错误: {e}")
        return {}
# 初始化品牌映射
BRAND_MAPPING = load_brand_mapping()

def ensure_dir_exists(directory):
    """确保目录存在，如果不存在则创建"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        logger.info(f"创建目录: {directory}")

def similar_text(a, b):
    """计算两个字符串的相似度"""
    return SequenceMatcher(None, a.lower(), b.lower()).ratio()

def clean_text(text):
    """清理文本，去除括号内容和多余空格"""
    # 移除括号及其内容
    text = re.sub(r'\([^)]*\)', '', text)
    # 移除方括号及其内容
    text = re.sub(r'\[[^\]]*\]', '', text)
    # 移除大括号及其内容
    text = re.sub(r'\{[^}]*\}', '', text)
    # 移除多余空格
    text = re.sub(r'\s+', ' ', text).strip()
    return text

def normalize_brand(brand):
    """标准化品牌名称"""
    if not brand:
        return brand
    
    brand_lower = brand.lower()
    
    # 检查映射
    if brand_lower in BRAND_MAPPING:
        return BRAND_MAPPING[brand_lower]
    
    # 返回标准化的品牌名称
    return brand_lower

def parse_rank_response(response, items):
    """解析rank提示的响应，将排名转换为有序列表"""
    cleaned_text = clean_text(response)
    items_lower = [item.lower() for item in items]
    result = []
    
    # 尝试从行号格式提取排名
    numbered_items = re.findall(r'\d+\.\s*([^\d\n.]+)', cleaned_text)
    if numbered_items:
        for item in numbered_items:
            item_text = item.strip()
            normalized_item = normalize_brand(item_text)
            
            # 检查是否与items列表中的任何品牌匹配
            found = False
            for idx, orig_item in enumerate(items):
                if normalize_brand(orig_item) == normalized_item:
                    if orig_item not in result:
                        result.append(orig_item)
                    found = True
                    break
            
            # 如果没有直接匹配，查找最相似的
            if not found:
                best_match = None
                best_sim = 0.7  # 提高阈值以确保更好的匹配
                
                for orig_item in items:
                    sim = similar_text(normalized_item, normalize_brand(orig_item))
                    if sim > best_sim and orig_item not in result:
                        best_sim = sim
                        best_match = orig_item
                
                if best_match:
                    result.append(best_match)
    
    # 如果从行号格式提取失败，尝试提取逗号或换行分隔的列表
    if not result:
        separators = re.split(r',|\n', cleaned_text)
        
        for item in separators:
            item_text = item.strip()
            if not item_text:
                continue
                
            normalized_item = normalize_brand(item_text)
            
            # 检查是否与items列表中的任何品牌匹配
            found = False
            for orig_item in items:
                if normalize_brand(orig_item) == normalized_item:
                    if orig_item not in result:
                        result.append(orig_item)
                    found = True
                    break
            
            # 如果没有直接匹配，查找最相似的
            if not found:
                best_match = None
                best_sim = 0.7  # 提高阈值
                
                for orig_item in items:
                    sim = similar_text(normalized_item, normalize_brand(orig_item))
                    if sim > best_sim and orig_item not in result:
                        best_sim = sim
                        best_match = orig_item
                
                if best_match:
                    result.append(best_match)
    
    # 确保所有原始项目都在结果中
    for item in items:
        if item not in result:
            result.append(item)
    
    # 截断结果使其长度与items相同
    return result[:len(items)]

def calculate_rank_consistency(responses):
    """计算多个响应之间排名顺序的一致性"""
    if not responses:
        return {}
    
    # 计算每个位置上的品牌出现频率
    position_freq = {}
    for response in responses:
        for pos, brand in enumerate(response):
            if pos not in position_freq:
                position_freq[pos] = {}
            position_freq[pos][brand] = position_freq[pos].get(brand, 0) + 1
    
    # 计算每个响应的一致性分数
    consistency_scores = {}
    for i, response in enumerate(responses):
        score = 0
        for pos, brand in enumerate(response):
            # 该位置上的品牌出现频率越高，分数越高
            freq = position_freq[pos].get(brand, 0)
            score += freq / len(responses)
        consistency_scores[i] = score
    
    return consistency_scores

def calculate_position_weight(pos, total_items):
    """计算排名位置的权重，前面的位置权重更高"""
    return 1 - (pos / total_items)  # 权重从1递减到0

def calculate_brand_similarity(brand1, brand2):
    """计算两个品牌名称的相似度，利用映射处理变体"""
    # 标准化名称
    brand1 = normalize_brand(brand1)
    brand2 = normalize_brand(brand2)
    
    # 如果标准化后完全相同，返回1.0
    if brand1 == brand2:
        return 1.0
    
    # 计算相似度
    return similar_text(brand1, brand2)

def calculate_rank_score(response, all_responses):
    """计算单个响应的综合分数"""
    score = 0
    total_items = len(response)
    
    # 计算位置一致性分数
    consistency_scores = calculate_rank_consistency(all_responses)
    score += consistency_scores[all_responses.index(response)] * 0.4  # 40%权重
    
    # 计算品牌相似度分数
    sim_score = 0
    for pos, item in enumerate(response):
        weight = calculate_position_weight(pos, total_items)
        # 与其他响应中相同位置的品牌比较相似度
        for other_response in all_responses:
            if pos < len(other_response):
                sim = calculate_brand_similarity(item, other_response[pos])
                sim_score += sim * weight
    score += (sim_score / len(all_responses)) * 0.6  # 60%权重
    
    return score

def select_best_response_rank(responses):
    """从多次rank响应中选择最佳响应"""
    if not responses:
        return None
    
    if len(responses) == 1:
        return responses[0]
    
    # 使用改进的排名响应选择策略
    scores = []
    for response in responses:
        score = calculate_rank_score(response, responses)
        scores.append(score)
    
    # 返回得分最高的响应
    best_index = scores.index(max(scores))
    return responses[best_index]

async def call_llm_async(prompt, client, max_retries=2):
    """异步调用LLM模型并获取响应，支持自动重试"""
    retries = 0
    while retries <= max_retries:
        try:
            # 使用generate方法替代chat方法
            response = await client.generate(
                model=MODEL_NAME,
                prompt=prompt
            )
            # generate方法返回的是response对象，直接获取response字段
            content = response['response']
            
            return content
        except Exception as e:
            retries += 1
            if retries > max_retries:
                logger.error(f"调用LLM失败（已重试{max_retries}次）: {e}")
                return ""
            else:
                logger.warning(f"调用LLM出错，正在进行第{retries}次重试: {e}")
                await asyncio.sleep(1 * retries)  # 随着重试次数增加等待时间

def save_batch_results(rank_results, batch_idx):
    """保存批次结果"""
    # 保存rank结果
    if rank_results:
        rank_df = pd.DataFrame(rank_results)
        rank_output_file = RANK_OUTPUT_FILE_TEMPLATE.format(batch_idx)
        rank_df.to_csv(rank_output_file, index=False)
        logger.info(f"已保存 {len(rank_df)} 条rank结果到 {rank_output_file}")

def merge_rank_results():
    """已弃用 - 合并功能已集成到主流程中"""
    logger.warning("merge_rank_results 函数已弃用，不再需要单独调用")
    return None

def load_selected_brands():
    """加载已保存的选定品牌列表"""
    # 如果指定了文件路径，直接使用
    if args.selected_brands_file:
        if os.path.exists(args.selected_brands_file):
            logger.info(f"使用指定的选定品牌文件: {args.selected_brands_file}")
            try:
                with open(args.selected_brands_file, 'r') as f:
                    brands = json.load(f)
                logger.info(f"成功加载 {len(brands)} 个选定品牌")
                return brands
            except Exception as e:
                logger.error(f"加载选定品牌列表时发生错误: {e}")
                return None
        else:
            logger.error(f"指定的选定品牌文件不存在: {args.selected_brands_file}")
            return None
    
    # 直接使用固定的文件名
    brands_file = os.path.join(MODEL_OUTPUT_DIR, 'selected_brands_standardized.json')
    
    if not os.path.exists(brands_file):
        logger.warning(f"未找到品牌文件: {brands_file}")
        return None
    
    logger.info(f"加载选定品牌列表: {brands_file}")
    
    try:
        with open(brands_file, 'r') as f:
            brands = json.load(f)
        logger.info(f"成功加载 {len(brands)} 个选定品牌")
        return brands
    except Exception as e:
        logger.error(f"加载选定品牌列表时发生错误: {e}")
        return None

def extract_brands_from_direct_results():
    """获取品牌列表，直接使用selected_brands_standardized.json"""
    selected_brands = load_selected_brands()
    if selected_brands:
        return selected_brands
    
    logger.error("无法加载品牌列表，请确保selected_brands_standardized.json文件存在")
    return None

async def process_prompt_async(row, client, semaphore, shuffled_brands):
    """异步处理单个提示"""
    prompt_text = row['prompt']
    sensitive_attribute = row['sensitive_attribute']
    age = row.get('age', '')
    gender = row.get('gender', '')
    race = row.get('race', '')
    occasion = row['occasion']
    season = row['season']
    prompt_type = row['prompt_type']  # 获取具体的rank类型
    
    # 运行多次并收集响应
    responses = []
    raw_responses = []
    
    # 使用信号量限制并发
    async with semaphore:
        for i in range(NUM_REPEATS):
            try:
                raw_response = await call_llm_async(prompt_text, client, max_retries=MAX_RETRIES)
                raw_responses.append(raw_response)
                
                parsed_response = parse_rank_response(raw_response, shuffled_brands)
                responses.append(parsed_response)
                
            except Exception as e:
                logger.error(f"处理rank提示时发生错误，第 {i+1} 次尝试: {e}")
                raw_responses.append("")  # 添加空响应以保持索引一致
            
            # 添加随机延迟以避免API限制
            await asyncio.sleep(random.uniform(DELAY_MIN, DELAY_MAX))
    
    # 如果没有收集到任何有效响应，则返回None
    if not responses:
        logger.warning(f"没有为rank提示收集到有效响应: {prompt_text}")
        return None
    
    # 选择最佳响应
    best_response = select_best_response_rank(responses)
    
    result = {
        'prompt_type': prompt_type,
        'sensitive_attribute': sensitive_attribute,
        'age': age,
        'gender': gender,
        'race': race,
        'occasion': occasion,
        'season': season,
        'prompt': prompt_text
    }
    
    # 添加排名项目
    if best_response is not None:
        for i, item in enumerate(best_response):
            result[f'rank_{i+1}'] = item
    
    # 添加原始响应
    for i, raw in enumerate(raw_responses):
        result[f'raw_response_{i+1}'] = raw
    
    return result

async def process_batch_async(batch_df, batch_idx, client, semaphore, shuffled_brands):
    """异步处理一个批次的提示"""
    logger.info(f"开始处理批次 {batch_idx}，包含 {len(batch_df)} 个提示")
    batch_start_time = time.time()
    
    # 创建任务列表
    tasks = [process_prompt_async(row, client, semaphore, shuffled_brands) for _, row in batch_df.iterrows()]
    
    # 使用tqdm显示进度
    results = []
    for f in tqdm(asyncio.as_completed(tasks), total=len(tasks), desc=f"批次{batch_idx+1}"):
        result = await f
        if result:
            results.append(result)
    
    # 计算并显示批次处理时间
    batch_end_time = time.time()
    batch_duration = batch_end_time - batch_start_time
    avg_prompt_time = batch_duration / len(batch_df) if len(batch_df) > 0 else 0
    logger.info(f"批次 {batch_idx+1} 完成: {batch_duration:.1f}秒, 均值 {avg_prompt_time:.1f}秒/提示")
    
    # 保存批次结果
    save_batch_results(results, batch_idx)
    
    return results

async def process_rank_prompts_async(batch_size=BATCH_SIZE):
    """异步处理rank提示并保存结果"""
    # 确保基础输出目录和模型特定目录都存在
    ensure_dir_exists(BASE_OUTPUT_DIR)
    ensure_dir_exists(MODEL_OUTPUT_DIR)
    ensure_dir_exists(DIRECT_OUTPUT_DIR)
    ensure_dir_exists(RANK_OUTPUT_DIR)
    
    # 读取提示文件
    try:
        prompts_df = pd.read_csv(PROMPT_FILE)
        logger.info(f"成功读取了 {len(prompts_df)} 个rank提示")
    except Exception as e:
        logger.error(f"读取提示文件时发生错误: {e}")
        return
    
    # 创建异步客户端
    client = ollama.AsyncClient()
    
    # 检查ollama服务是否可用
    try:
        # 使用generate方法替代chat方法进行测试
        test_response = await client.generate(model=MODEL_NAME, prompt="test")
        logger.info(f"ollama服务正常，模型 {MODEL_NAME} 可用")
    except Exception as e:
        logger.error(f"无法连接到ollama服务: {e}")
        logger.info("请确保ollama服务已启动，并且已安装所需模型")
        return
    
    # 从direct结果中获取选定的品牌列表
    selected_brands = extract_brands_from_direct_results()
    
    if not selected_brands or len(selected_brands) < MIN_BRANDS_FOR_RANKING:
        logger.error(f"选定的品牌数量不足 ({len(selected_brands) if selected_brands else 0}), 无法执行rank任务")
        return
    
    logger.info(f"用于rank任务的品牌: {', '.join(selected_brands)}")
    
    # 打乱品牌列表顺序
    shuffled_brands = selected_brands.copy()
    random.shuffle(shuffled_brands)
    logger.info(f"已打乱品牌列表顺序用于rank任务")
    
    # 品牌列表格式化为逗号分隔字符串
    brands_str = ", ".join(shuffled_brands)
    
    # 替换rank提示中的品牌列表占位符
    prompts_df['prompt'] = prompts_df['prompt'].str.replace("{BRAND_LIST}", brands_str)
    
    # 计算批次数量
    total_prompts = len(prompts_df)
    num_batches = math.ceil(total_prompts / batch_size)
    
    # 创建批次索引
    batch_indices = [(i, min(i + batch_size, total_prompts)) for i in range(0, total_prompts, batch_size)]
    
    # 存储所有rank结果
    all_rank_results = []
    
    # 创建信号量以限制并发请求数
    semaphore = asyncio.Semaphore(MAX_CONCURRENCY)
    
    # 使用tqdm显示总体进度
    with tqdm(total=len(batch_indices), desc="Rank任务进度") as pbar_batches:
        # 遍历每个批次
        for batch_idx, (start_idx, end_idx) in enumerate(batch_indices):
            current_batch = prompts_df.iloc[start_idx:end_idx].copy()
            logger.info(f"Rank批次 {batch_idx+1}/{num_batches}: {len(current_batch)}个提示")
            
            try:
                # 处理当前批次
                batch_results = await process_batch_async(current_batch, batch_idx, client, semaphore, shuffled_brands)
                
                # 添加到所有结果中
                all_rank_results.extend(batch_results)
                
            except Exception as e:
                logger.error(f"处理batch {batch_idx} 时发生错误: {e}")
            
            # 更新总批次进度条
            pbar_batches.update(1)
    
    # 直接保存所有累积的结果到最终文件
    if all_rank_results:
        final_df = pd.DataFrame(all_rank_results)
        final_df.to_csv(RANK_FINAL_OUTPUT_FILE, index=False)
        logger.info(f"已保存 {len(final_df)} 条rank最终结果到 {RANK_FINAL_OUTPUT_FILE}")
    
    return all_rank_results

async def main_async():
    """异步主函数"""
    print(f"Rank任务测试配置:")
    print(f"- 模型: {MODEL_NAME}")
    print(f"- 重复次数: {NUM_REPEATS}")
    print(f"- 批次大小: {BATCH_SIZE}")
    print(f"- 最大并发数: {MAX_CONCURRENCY}")
    print(f"- 最大重试次数: {MAX_RETRIES}")
    print(f"- API调用延迟: {DELAY_MIN}-{DELAY_MAX}秒")
    print(f"- 结果保存路径: {RANK_OUTPUT_DIR}")
    print("")
    
    start_time = time.time()
    logger.info(f"开始Rank实验 - 模型: {MODEL_NAME}, 重复次数: {NUM_REPEATS}, 批次大小: {BATCH_SIZE}, 并发数: {MAX_CONCURRENCY}")
    logger.info(f"实验结果将保存到目录: {RANK_OUTPUT_DIR}")
    
    # 处理提示
    await process_rank_prompts_async(batch_size=BATCH_SIZE)
    
    end_time = time.time()
    total_time = end_time - start_time
    hours, remainder = divmod(total_time, 3600)
    minutes, seconds = divmod(remainder, 60)
    
    print(f"\nRank实验完成! 总用时: {int(hours)}小时 {int(minutes)}分钟 {int(seconds)}秒")
    logger.info("Rank实验完成")

def main():
    """主函数入口点"""
    asyncio.run(main_async())

if __name__ == "__main__":
    main()