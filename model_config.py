#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
模型配置文件
统一管理支持的模型及其配置
"""

# 支持的模型配置
SUPPORTED_MODELS = {
    'qwen': {
        'name': 'qwen2.5:7b',
        'display_name': 'Qwen 2.5 7B'
    },
    'llama': {
        'name': 'llama3.1:8b',
        'display_name': 'Llama 3.1 8B'
    },
    'mistral': {
        'name': 'mistral',
        'display_name': 'Mistral'
    },
    'gemma': {
        'name': 'gemma2',
        'display_name': 'Gemma 2'
    },
    'glm': {
        'name': 'glm4',
        'display_name': 'GLM 4'
    },
    'gemma3': {
        'name': 'gemma3',
        'display_name': 'Gemma 3'
    },
    'internlm2': {
        'name': 'internlm2',
        'display_name': 'InternLM 2'
    }
}

# 默认模型
DEFAULT_MODEL = 'llama'

def get_model_name(model_key):
    """根据模型键获取模型名称"""
    if model_key not in SUPPORTED_MODELS:
        raise ValueError(f"不支持的模型: {model_key}. 支持的模型: {list(SUPPORTED_MODELS.keys())}")
    return SUPPORTED_MODELS[model_key]['name']

def get_model_display_name(model_key):
    """根据模型键获取模型显示名称"""
    if model_key not in SUPPORTED_MODELS:
        raise ValueError(f"不支持的模型: {model_key}. 支持的模型: {list(SUPPORTED_MODELS.keys())}")
    return SUPPORTED_MODELS[model_key]['display_name']

def get_supported_model_choices():
    """获取支持的模型选择列表"""
    return list(SUPPORTED_MODELS.keys())

def get_models_help_text():
    """获取模型选择的帮助文本"""
    model_list = ', '.join(SUPPORTED_MODELS.keys())
    return f'选择模型类型: {model_list}'
