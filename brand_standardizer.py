import os
import re
import pandas as pd
import json
import logging
from difflib import SequenceMatcher
import argparse
from datetime import datetime
from log_config import setup_logger
#在生成的标准化统计文件中暂时只统计direct任务，没有统计fill_in_blank任务
# 配置日志
logger = setup_logger("brand_standardize.log")

# 解析命令行参数
parser = argparse.ArgumentParser(description='品牌标准化工具 - 支持direct和fill_in_blank任务')
# 移除 --backup 参数
parser.add_argument('--model', type=str, help='只处理特定模型的结果，不指定则处理所有模型')
parser.add_argument('--tasks', type=str, nargs='+', default=['direct', 'fill_in_blank'], 
                    help='指定要处理的开放式任务类型，默认处理direct和fill_in_blank')
args = parser.parse_args()

# 基础路径
BASE_OUTPUT_DIR = "results"

# 品牌名称的标准化映射
BRAND_MAPPING_FILE = "brand_mapping.json"

# 加载品牌映射
def load_brand_mapping():
    try:
        with open(BRAND_MAPPING_FILE, 'r') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载品牌映射文件时发生错误: {e}")
        return {}
# 初始化品牌映射
BRAND_MAPPING = load_brand_mapping()

# 最大品牌数量
MAX_BRANDS_FOR_RANKING = 10

def ensure_dir_exists(directory):
    """确保目录存在，如果不存在则创建"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        logger.info(f"创建目录: {directory}")

def similar_text(a, b):
    """计算两个字符串的相似度"""
    return SequenceMatcher(None, a.lower(), b.lower()).ratio()

def normalize_brand(brand):
    """标准化品牌名称"""
    if not brand:
        return brand
    
    brand_lower = brand.lower()
    
    # 检查映射
    if brand_lower in BRAND_MAPPING:
        # 直接返回映射后的品牌名，不再进行额外处理
        return BRAND_MAPPING[brand_lower]
    
    # 返回标准化的品牌名称（首字母大写）
    return capitalize_brand(brand_lower)

def capitalize_brand(brand):
    """将品牌名称的首字母大写"""
    if not brand:
        return brand
    
    # 普通品牌名称，按空格分割后每个单词首字母大写
    return ' '.join(word.capitalize() for word in brand.split())

def analyze_brand_frequencies(open_ended_results, model_dir, timestamp=None): 
    """分析品牌频率并选择用于排名任务的品牌 - 只统计sensitive_attribute为combined的数据"""
    from collections import Counter
    
    brand_counter = Counter()
    total_records = len(open_ended_results)
    filtered_records = 0
    
    for result in open_ended_results:
        # 只统计sensitive_attribute为combined的数据
        if result.get('sensitive_attribute') == 'combined':
            if 'brand' in result and result['brand']:
                brand = normalize_brand(result['brand'])
                if brand:  # 确保不添加空字符串
                    brand_counter[brand] += 1
                    filtered_records += 1
    
    logger.info(f"总记录数: {total_records}, 用于统计的记录数 (sensitive_attribute=combined): {filtered_records}")
    
    # 按频率排序
    # 首先按品牌名称字母顺序排序，确保相同频率的品牌排序稳定
    sorted_items = sorted(brand_counter.items(), key=lambda item: item[0])
    # 然后按频率降序排序
    sorted_brands = sorted(sorted_items, key=lambda item: item[1], reverse=True)
    
    # 保存品牌统计结果
    brand_stats_file = os.path.join(model_dir, "brand_statistics_standardized.json")
    brand_stats = {brand: count for brand, count in sorted_brands}
    
    with open(brand_stats_file, 'w') as f:
        json.dump(brand_stats, f, indent=2)
    logger.info(f"已保存标准化后的品牌统计结果到 {brand_stats_file}")
    
    # 选择前N个品牌
    selected_brands = [brand for brand, _ in sorted_brands[:MAX_BRANDS_FOR_RANKING]]
    
    # 保存选定的品牌列表
    selected_brands_file = os.path.join(model_dir, "selected_brands_standardized.json")
    with open(selected_brands_file, 'w') as f:
        json.dump(selected_brands, f, indent=2)
    logger.info(f"已保存标准化后的选定品牌列表到 {selected_brands_file}")
    
    return selected_brands, brand_stats_file, selected_brands_file

def get_latest_file(directory, prefix, suffix, exclude_pattern=None):
    """获取目录中最新的符合条件的文件，可选择排除特定模式"""
    matching_files = []
    for f in os.listdir(directory):
        if f.startswith(prefix) and f.endswith(suffix):
            if exclude_pattern and exclude_pattern in f:
                continue
            matching_files.append(f)
            
    if not matching_files:
        return None
    
    # 按文件名（通常包含时间戳）排序，获取最新的文件
    matching_files.sort(reverse=True)
    return os.path.join(directory, matching_files[0])

def load_json_file(file_path):
    """加载JSON文件"""
    try:
        with open(file_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载JSON文件时发生错误: {e}")
        return None

def compare_brand_lists(original_brands, new_brands):
    """比较两个品牌列表，返回差异"""
    added = [b for b in new_brands if b not in original_brands]
    removed = [b for b in original_brands if b not in new_brands]
    
    return added, removed

def standardize_open_ended_results(model_dir, task_type):
    """标准化指定模型目录中的开放式任务结果 (direct 或 fill_in_blank)"""
    # 构造任务目录路径
    task_dir = os.path.join(model_dir, task_type)
    
    # 检查任务目录是否存在
    if not os.path.exists(task_dir):
        logger.warning(f"{task_type}目录不存在: {task_dir}")
        return None, None, None
        
    # 查找最新的final文件
    final_file = None
    for f in os.listdir(task_dir):
        if f.startswith('final_') and f.endswith('.csv'):
            if final_file is None or f > final_file:
                final_file = f
    
    if not final_file:
        logger.warning(f"在目录 {task_dir} 中未找到{task_type}结果文件")
        return None, None, None
    
    final_path = os.path.join(task_dir, final_file)
    logger.info(f"处理{task_type}结果文件: {final_path}")
    
    try:
        # 读取CSV文件
        df = pd.read_csv(final_path)
        
        # 保存原始品牌列用于比较（在任何修改之前）
        original_brands = df['brand'].tolist()
        
        # 创建一个新的DataFrame用于标准化结果，避免修改原始df
        standardized_df = df.copy()
        
        # 标准化品牌列
        standardized_df['brand'] = standardized_df['brand'].apply(normalize_brand)
        
        # 计算有多少品牌被标准化（比较原始和标准化后的品牌）
        changed_count = sum(1 for i, original_brand in enumerate(original_brands) 
                          if original_brand != standardized_df['brand'].iloc[i])
        logger.info(f"标准化了 {changed_count} 个品牌名称")
        
        # 保存标准化后的文件
        # 构建新文件名
        standardized_file = os.path.join(task_dir, f"{task_type}_standardized.csv")
        standardized_df.to_csv(standardized_file, index=False)
        logger.info(f"已保存标准化结果到 {standardized_file}")
        
        return standardized_df.to_dict('records'), changed_count, standardized_file
        
    except Exception as e:
        logger.error(f"处理文件 {final_path} 时发生错误: {e}")
        return None, None, None

def combine_open_ended_results(model_dir, task_types=['direct', 'fill_in_blank']):
    """处理多种开放式任务，但只基于direct任务生成品牌统计和选择"""
    all_results = []
    all_changed_counts = 0
    processed_tasks = []
    direct_results = None
    
    for task_type in task_types:
        results, changed_count, standardized_file = standardize_open_ended_results(model_dir, task_type)
        if results is not None:
            all_results.extend(results)
            all_changed_counts += changed_count if changed_count else 0
            processed_tasks.append(task_type)
            logger.info(f"成功处理{task_type}任务，获得{len(results)}条结果")
            
            # 保存direct任务的结果用于品牌分析
            if task_type == 'direct':
                direct_results = results
        else:
            logger.warning(f"跳过{task_type}任务，未找到有效结果")
    
    if not all_results:
        logger.error("未找到任何有效的开放式任务结果")
        return None, None, None
    
    logger.info(f"合并了{len(processed_tasks)}种任务类型的结果，总计{len(all_results)}条记录")
    logger.info(f"总共标准化了{all_changed_counts}个品牌名称")
    
    # 只使用direct任务的结果进行品牌频率分析
    if direct_results is None:
        logger.error("未找到direct任务结果，无法生成品牌统计")
        return None, None, None
    
    logger.info(f"基于direct任务中sensitive_attribute=combined的记录生成品牌统计和选择")
    new_brands, stats_file, selected_file = analyze_brand_frequencies(direct_results, model_dir, "")
    
    # 加载原始选定的品牌列表进行比较
    original_selected_file = os.path.join(model_dir, "selected_brands.json")
    
    if os.path.exists(original_selected_file):
        # 调试输出
        logger.info(f"找到原选定品牌文件: {original_selected_file}")
        logger.info(f"新生成的品牌文件: {selected_file}")
        
        # 排除自身
        if os.path.abspath(original_selected_file) != os.path.abspath(selected_file):
            original_selected_brands = load_json_file(original_selected_file)
            if original_selected_brands:
                logger.info(f"加载原选定品牌列表: {original_selected_file}")
                
                # 直接比较两个品牌列表
                if original_selected_brands != new_brands:
                    added, removed = compare_brand_lists(original_selected_brands, new_brands)
                    logger.info(f"品牌列表发生变化，添加: {len(added)}，移除: {len(removed)}")
                    return new_brands, original_selected_brands, (added, removed)
                else:
                    logger.info("品牌列表未发生变化")
    
    return new_brands, None, None

def process_all_models():
    """处理所有模型目录"""
    ensure_dir_exists(BASE_OUTPUT_DIR)
    
    # 获取所有模型目录
    model_dirs = []
    if args.model:
        # 只处理指定模型
        model_dir = os.path.join(BASE_OUTPUT_DIR, args.model)
        if os.path.isdir(model_dir):
            # 确保模型下的任务目录存在
            for task_type in args.tasks:
                task_dir = os.path.join(model_dir, task_type)
                ensure_dir_exists(task_dir)
            model_dirs.append((args.model, model_dir))
        else:
            logger.error(f"指定的模型目录不存在: {model_dir}")
            return
    else:
        # 处理所有模型
        for item in os.listdir(BASE_OUTPUT_DIR):
            model_dir = os.path.join(BASE_OUTPUT_DIR, item)
            if os.path.isdir(model_dir):
                # 确保模型下的任务目录存在
                for task_type in args.tasks:
                    task_dir = os.path.join(model_dir, task_type)
                    ensure_dir_exists(task_dir)
                model_dirs.append((item, model_dir))
    
    if not model_dirs:
        logger.warning(f"在 {BASE_OUTPUT_DIR} 中未找到任何模型目录")
        return
    
    logger.info(f"发现 {len(model_dirs)} 个模型目录")
    
    # 处理每个模型目录
    changes_detected = False
    for model_name, model_dir in model_dirs:
        logger.info(f"处理模型 {model_name}")
        new_brands, original_brands, diff = combine_open_ended_results(model_dir, args.tasks)
        
        if diff:
            added, removed = diff
            changes_detected = True
            
            print(f"\n{'='*20} 模型: {model_name} {'='*20}")
            print(f"原品牌列表: {', '.join(original_brands)}")
            print(f"新品牌列表: {', '.join(new_brands)}")
            print(f"\n添加的品牌: {', '.join(added) if added else '无'}")
            print(f"移除的品牌: {', '.join(removed) if removed else '无'}")
            print(f"\n>>> 建议重新运行rank任务: python llm_tester_llamaV3.py --model {model_name} --skip_direct --force_reanalyze")
            print(f"{'='*50}\n")
    
    if not changes_detected:
        print("\n所有模型的品牌选择均未发生变化，无需重新运行rank任务。")

def main():
    """主函数"""
    print("品牌标准化工具 - 支持开放式任务")
    # print(f"备份模式: {'启用' if args.backup else '禁用'}")
    print(f"处理模型: {args.model if args.model else '所有模型'}")
    print(f"处理任务类型: {', '.join(args.tasks)}")
    print("")
    
    process_all_models()
    
    print("\n处理完成!")

if __name__ == "__main__":
    main()