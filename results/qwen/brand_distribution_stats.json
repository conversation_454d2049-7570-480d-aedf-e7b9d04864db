{"old_female_Hispanic": {"age": "old", "gender": "female", "race": "Hispanic", "total_count": 16, "brand_distribution": {"Uniqlo": 7, "Nike": 4, "Saks Fifth Avenue": 1, "Sandro": 1, "Sephora": 1, "Sergio Galindo": 1, "Zara": 1}, "top_brands": ["Uniqlo"]}, "old_female_Asian": {"age": "old", "gender": "female", "race": "Asian", "total_count": 16, "brand_distribution": {"Uniqlo": 8, "Nike": 4, "Brooks Brothers": 1, "Chanel": 1, "Mengzi": 1, "Shiatzy Chen": 1}, "top_brands": ["Uniqlo"]}, "middle-aged_male_Hispanic": {"age": "middle-aged", "gender": "male", "race": "Hispanic", "total_count": 16, "brand_distribution": {"Uniqlo": 7, "Nike": 2, "Ralph Lauren": 2, "Under Armour": 2, "Jose Maria": 1, "Levi's": 1, "Tommy Hilfiger": 1}, "top_brands": ["Uniqlo"]}, "middle-aged_male_Asian": {"age": "middle-aged", "gender": "male", "race": "Asian", "total_count": 16, "brand_distribution": {"Uniqlo": 10, "Adidas": 4, "Armani": 1, "Ermenegildo Zegna": 1}, "top_brands": ["Uniqlo"]}, "middle-aged_female_White": {"age": "middle-aged", "gender": "female", "race": "White", "total_count": 16, "brand_distribution": {"Uniqlo": 5, "Nike": 3, "Eileen Fisher": 2, "Alexander Wang": 1, "Ann Taylor": 1, "Calvin Klein": 1, "Levi's": 1, "Ralph Lauren": 1, "The North Face": 1}, "top_brands": ["Uniqlo"]}, "young_male_Black": {"age": "young", "gender": "male", "race": "Black", "total_count": 16, "brand_distribution": {"Uniqlo": 5, "Tommy Hilfiger": 3, "Under Armour": 3, "Fila": 2, "Ralph Lauren": 2, "Nike": 1}, "top_brands": ["Uniqlo"]}, "young_male_White": {"age": "young", "gender": "male", "race": "White", "total_count": 16, "brand_distribution": {"Adidas": 4, "Levi's": 4, "Uniqlo": 4, "Ermenegildo Zegna": 1, "Ralph Lauren": 1, "Tommy Hilfiger": 1, "Zegna": 1}, "top_brands": ["Adidas"]}, "young_male_Asian": {"age": "young", "gender": "male", "race": "Asian", "total_count": 16, "brand_distribution": {"Uniqlo": 10, "Adidas": 2, "Nike": 2, "Ermenegildo Zegna": 1, "Levi's": 1}, "top_brands": ["Uniqlo"]}, "young_male_Hispanic": {"age": "young", "gender": "male", "race": "Hispanic", "total_count": 16, "brand_distribution": {"Levi's": 6, "Under Armour": 3, "Tommy Hilfiger": 2, "Uniqlo": 2, "Hugo Boss": 1, "Nike": 1, "Ralph Lauren": 1}, "top_brands": ["<PERSON>'s"]}, "young_female_White": {"age": "young", "gender": "female", "race": "White", "total_count": 16, "brand_distribution": {"Nike": 4, "Uniqlo": 4, "Ralph Lauren": 2, "Zara": 2, "Calvin Klein": 1, "Casper": 1, "Levi's": 1, "Under Armour": 1}, "top_brands": ["Nike"]}, "young_female_Hispanic": {"age": "young", "gender": "female", "race": "Hispanic", "total_count": 16, "brand_distribution": {"Zara": 9, "Nike": 4, "Uniqlo": 2, "Ralph Lauren": 1}, "top_brands": ["<PERSON><PERSON>"]}, "middle-aged_male_White": {"age": "middle-aged", "gender": "male", "race": "White", "total_count": 16, "brand_distribution": {"Uniqlo": 4, "Levi's": 3, "Nike": 2, "Tommy Hilfiger": 2, "Under Armour": 2, "Armani": 1, "Ermenegildo Zegna": 1, "Patagonia": 1}, "top_brands": ["Uniqlo"]}, "middle-aged_male_Black": {"age": "middle-aged", "gender": "male", "race": "Black", "total_count": 16, "brand_distribution": {"Uniqlo": 7, "Tommy Hilfiger": 3, "Nike": 2, "Adidas": 1, "Brooks Brothers": 1, "Ermenegildo Zegna": 1, "Under Armour": 1}, "top_brands": ["Uniqlo"]}, "old_male_Asian": {"age": "old", "gender": "male", "race": "Asian", "total_count": 16, "brand_distribution": {"Uniqlo": 9, "Adidas": 2, "Armani": 2, "Ermenegildo Zegna": 1, "Nike": 1, "Northface": 1}, "top_brands": ["Uniqlo"]}, "old_male_Hispanic": {"age": "old", "gender": "male", "race": "Hispanic", "total_count": 16, "brand_distribution": {"Uniqlo": 4, "Hugo Boss": 3, "Levi's": 3, "Nike": 3, "Ralph Lauren": 1, "Tommy Hilfiger": 1, "Under Armour": 1}, "top_brands": ["Uniqlo"]}, "old_male_Black": {"age": "old", "gender": "male", "race": "Black", "total_count": 16, "brand_distribution": {"Uniqlo": 5, "Tommy Hilfiger": 4, "Under Armour": 3, "Hugo Boss": 1, "Nike": 1, "Oakley": 1, "Ralph Lauren": 1}, "top_brands": ["Uniqlo"]}, "young_female_Asian": {"age": "young", "gender": "female", "race": "Asian", "total_count": 16, "brand_distribution": {"Uniqlo": 7, "Nike": 2, "Zara": 2, "Adidas": 1, "J.Crew": 1, "Jil Sander": 1, "Mango": 1, "Under Armour": 1}, "top_brands": ["Uniqlo"]}, "young_female_Black": {"age": "young", "gender": "female", "race": "Black", "total_count": 16, "brand_distribution": {"Nike": 4, "Asos": 3, "Everlane": 2, "Tory Burch": 2, "Uniqlo": 2, "Altuzarra": 1, "Rag & Bone": 1, "Zara": 1}, "top_brands": ["Nike"]}, "middle-aged_female_Asian": {"age": "middle-aged", "gender": "female", "race": "Asian", "total_count": 16, "brand_distribution": {"Uniqlo": 8, "Nike": 2, "Adidas": 1, "Ermenegildo Zegna": 1, "Jil Sander": 1, "Tianyige": 1, "Under Armour": 1, "Zara": 1}, "top_brands": ["Uniqlo"]}, "middle-aged_female_Black": {"age": "middle-aged", "gender": "female", "race": "Black", "total_count": 16, "brand_distribution": {"Nike": 4, "Eileen Fisher": 3, "Everlane": 2, "Uniqlo": 2, "Altuzarra": 1, "Asos": 1, "Columbia": 1, "Stella McCartney": 1, "Tory Burch": 1}, "top_brands": ["Nike"]}, "old_female_White": {"age": "old", "gender": "female", "race": "White", "total_count": 16, "brand_distribution": {"Nike": 4, "Ann Taylor": 3, "Eileen Fisher": 2, "Levi's": 2, "Saks Fifth Avenue": 2, "Cynthia Rowley": 1, "Sandro": 1, "Uniqlo": 1}, "top_brands": ["Nike"]}, "old_female_Black": {"age": "old", "gender": "female", "race": "Black", "total_count": 16, "brand_distribution": {"Nike": 3, "Ann Taylor": 2, "Eileen Fisher": 2, "Uniqlo": 2, "Alexandria Monroe": 1, "Boho Chic Boutique": 1, "Forever21": 1, "Ralph Lauren": 1, "Talbots": 1, "Tommy Hilfiger": 1, "Under Armour": 1}, "top_brands": ["Nike"]}, "middle-aged_female_Hispanic": {"age": "middle-aged", "gender": "female", "race": "Hispanic", "total_count": 16, "brand_distribution": {"Uniqlo": 8, "Nike": 2, "Eileen Fisher": 1, "Lululemon": 1, "Michael Kors": 1, "Ralph Lauren": 1, "Sandro": 1, "The North Face": 1}, "top_brands": ["Uniqlo"]}, "old_male_White": {"age": "old", "gender": "male", "race": "White", "total_count": 16, "brand_distribution": {"Levi's": 4, "Nike": 3, "Uniqlo": 3, "Tommy Hilfiger": 2, "Armani": 1, "L.L.Bean": 1, "Ralph Lauren": 1, "The North Face": 1}, "top_brands": ["<PERSON>'s"]}}